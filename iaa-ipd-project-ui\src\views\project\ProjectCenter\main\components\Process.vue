<template>
  <div class="floating-panel-container">
    <!-- 浮动面板 -->
    <div
      class="floating-panel"
      :class="{ 'expanded': isExpanded }"
      :style="{ transform: `translateY(${panelTranslateY}px)` }"
    >
      <!-- 拖拽手柄 -->
      <div
        class="panel-handle"
        @click="togglePanel"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
        @mousedown="handleMouseDown"
        @mousemove="handleMouseMove"
        @mouseup="handleMouseUp"
      >
        <div class="handle-bar"></div>
        <div class="panel-title">项目进度</div>
      </div>

      <!-- 面板内容 -->
      <div class="panel-content" v-show="isExpanded">
        <!-- 进度条区域 -->
        <div class="progress-section">
          <el-slider
            v-model="tempProcess"
            :step="1"
            :min="0"
            :max="100"
            :class="{ 'slider-process': tempProcess >= 5, 'completed-process': tempProcess >= 100 }"
            :disabled="true"
          />
          <div class="process-scale">
            <div
              v-for="(item, index) in marks"
              :key="index"
              :style="`text-align:center;position: absolute;left:${index}%;${item.style};`"
              :class="[index > 1 ? 'cursor-pointer' : '']"
              @click="onNodeClick(item)"
            >
              <div class="slider__stop"></div>
              <div class="mt-1rem h-.85rem">{{ item.label }}</div>
              <div :class="[item.date == '实际' ? '!text-black' : '', 'h-.85rem']">{{ item.date }}</div>
              <div class="!text-black">{{ item.actualDate }}</div>
            </div>
          </div>
        </div>

        <!-- 详细信息区域 -->
        <div class="details-section">
          <div class="progress-info">
            <div class="info-item">
              <span class="label">当前进度:</span>
              <span class="value">{{ tempProcess }}%</span>
            </div>
            <div class="info-item">
              <span class="label">节点总数:</span>
              <span class="value">{{ Object.keys(marks).length - 2 }}</span>
            </div>
          </div>

          <!-- 节点列表 -->
          <div class="nodes-list">
            <div class="list-title">关键节点</div>
            <div
              v-for="(item, index) in filteredMarks"
              :key="index"
              class="node-item"
              :class="{ 'clickable': item.id }"
              @click="onNodeClick(item)"
            >
              <div class="node-info">
                <div class="node-name">{{ item.label }}</div>
                <div class="node-dates">
                  <span v-if="item.date" class="actual-date">实际: {{ item.date }}</span>
                  <span v-if="item.actualDate" class="plan-date">计划: {{ item.actualDate }}</span>
                </div>
              </div>
              <div class="node-arrow" v-if="item.id">
                <el-icon><ArrowRight /></el-icon>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 遮罩层 -->
    <div
      class="panel-overlay"
      v-show="isExpanded"
      @click="closePanel"
    ></div>
  </div>
</template>

<script lang="ts" setup>
import { dateUtil } from '@/utils/dateUtil'
import { formatDate } from '@/utils/formatTime'
import { propTypes } from '@/utils/propTypes'
import { BasicsApi } from '@/api/project/basics'
import { useCache } from '@/hooks/web/useCache'
import { ArrowRight } from '@element-plus/icons-vue'

const props = defineProps({
  process: propTypes.number.def(5),
  keyNodeDate: propTypes.oneOfType<any[]>([]).def([]),
  createDate: propTypes.string.def('')
})
const tempProcess = ref(props.process) //组件内进度条，这么处理的原因是取消修改复位

// 浮动面板状态
const isExpanded = ref(false)
const panelTranslateY = ref(0)
const isDragging = ref(false)
const startY = ref(0)
const startTranslateY = ref(0)

// 面板高度配置
const PANEL_COLLAPSED_HEIGHT = 60 // 收起时的高度
const PANEL_EXPANDED_HEIGHT = 400 // 展开时的高度
const DRAG_THRESHOLD = 50 // 拖拽阈值
/** 定义标记 */
interface Mark {
  id: number | undefined
  basicsId: number | undefined
  stage: number | undefined
  style: string
  label: string
  date: string
  actualDate: string
}
type Marks = Record<number, Mark>
const marks = ref<Marks>({})

const { wsCache } = useCache()
const router = useRouter()

const initMarks = async () => {
  marks.value = {}

  marks.value[0.5] = {
    id: undefined,
    basicsId: undefined,
    stage: undefined,
    style: 'color:var(--secondary-text-color)',
    label: '创建',
    date: dateUtil(props.createDate).format('YYYY-MM-DD'),
    actualDate: ''
  }
  marks.value[5] = {
    id: undefined,
    basicsId: undefined,
    stage: undefined,
    style: 'color:var(--secondary-text-color)',
    label: '',
    date: '实际',
    actualDate: '计划'
  }
  //均匀分割进度
  const segments = divideIntoSegments(props.keyNodeDate.length)
  let row = 0
  let startOverdue: number | null = null // 使用null表示未初始化
  for (const key of segments) {
    const dateValue = props.keyNodeDate[row].initEndDate
    const initEndDate = dateUtil(dateValue)
    let diffDays = 0

    // 计算延期/剩余天数
    if (props.keyNodeDate[row].progress < 100) {
      diffDays = dateUtil().diff(initEndDate, 'days')
      // 按顺序处理，只有碰到第一个延期节点时才记录延期天数
      if (startOverdue === null && diffDays > 0) {
        startOverdue = diffDays
      }
    } else if (props.keyNodeDate[row].progress == 100) {
      diffDays = dateUtil(props.keyNodeDate[row].completedDate).diff(initEndDate, 'days')
    }

    // 确保startOverdue有默认值
    const actualStartOverdue = startOverdue !== null ? startOverdue : 0

    // 颜色设置逻辑：延期用红色，否则用灰色
    const isOverdue = props.keyNodeDate[row].progress == 100 ? diffDays > 0 : actualStartOverdue > 0
    const colorStyle = isOverdue ? 'var(--el-color-danger)' : 'var(--secondary-text-color)'

    // 标签显示逻辑
    let statusText = ''
    let dayCount = 0

    // 需要显示状态的节点
    if (props.keyNodeDate[row].progress == 100) {
      // 已完成节点显示实际延期或剩余天数
      if (diffDays > 0) {
        statusText = '实延'
        dayCount = diffDays
      } else {
        statusText = '剩余'
        dayCount = Math.abs(diffDays)
      }
    } else {
      // 未完成节点显示统一的延期天数或剩余天数
      if (actualStartOverdue > 0) {
        statusText = '预延'
        dayCount = actualStartOverdue // 使用统一的延期天数
      } else {
        statusText = '剩余'
        dayCount = Math.abs(diffDays) // 显示各自的剩余天数
      }
    }
    if (dayCount == 0) {
      statusText = ''
    }
    marks.value[key] = {
      id: props.keyNodeDate[row].id,
      basicsId: props.keyNodeDate[row].basicsId,
      stage: props.keyNodeDate[row].stage,
      style: `color: ${colorStyle}`,
      label:
        cleanName(props.keyNodeDate[row].name) + (statusText ? `${statusText}${dayCount}天` : ''),
      date: props.keyNodeDate[row].completedDate
        ? formatDate(props.keyNodeDate[row].completedDate, 'YYYY-MM-DD')
        : '',
      actualDate: dateValue
    }
    row++
  }
}

// 修改方法为以下内容（移除中文保留逻辑）
const cleanName = (name: string): string => {
  if (name === '签发任务书') return name // 保留特殊处理
  return name.replace(/[^a-zA-Z0-9]/g, '') // 仅保留字母数字
}
function divideIntoSegments(n: number): number[] {
  // 计算每段长度
  const segmentLength = 100 / (n + 1)
  // 生成分割点数组
  const segments = [] as number[]
  for (let i = 1; i <= n; i++) {
    segments.push(Number((i * segmentLength).toFixed(0)))
  }
  return segments
}

// 浮动面板交互方法
const togglePanel = () => {
  isExpanded.value = !isExpanded.value
  updatePanelPosition()
}

const closePanel = () => {
  isExpanded.value = false
  updatePanelPosition()
}

const updatePanelPosition = () => {
  if (isExpanded.value) {
    panelTranslateY.value = -PANEL_EXPANDED_HEIGHT + PANEL_COLLAPSED_HEIGHT
  } else {
    panelTranslateY.value = 0
  }
}

// 触摸事件处理
const handleTouchStart = (e: TouchEvent) => {
  isDragging.value = true
  startY.value = e.touches[0].clientY
  startTranslateY.value = panelTranslateY.value
}

const handleTouchMove = (e: TouchEvent) => {
  if (!isDragging.value) return
  e.preventDefault()

  const currentY = e.touches[0].clientY
  const deltaY = currentY - startY.value
  const newTranslateY = startTranslateY.value + deltaY

  // 限制拖拽范围
  const minTranslateY = -PANEL_EXPANDED_HEIGHT + PANEL_COLLAPSED_HEIGHT
  const maxTranslateY = 0

  panelTranslateY.value = Math.max(minTranslateY, Math.min(maxTranslateY, newTranslateY))
}

const handleTouchEnd = () => {
  if (!isDragging.value) return
  isDragging.value = false

  // 根据拖拽距离决定展开或收起
  const threshold = -DRAG_THRESHOLD
  if (panelTranslateY.value < threshold) {
    isExpanded.value = true
  } else {
    isExpanded.value = false
  }
  updatePanelPosition()
}

// 鼠标事件处理（桌面端）
const handleMouseDown = (e: MouseEvent) => {
  isDragging.value = true
  startY.value = e.clientY
  startTranslateY.value = panelTranslateY.value
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

const handleMouseMove = (e: MouseEvent) => {
  if (!isDragging.value) return
  e.preventDefault()

  const currentY = e.clientY
  const deltaY = currentY - startY.value
  const newTranslateY = startTranslateY.value + deltaY

  // 限制拖拽范围
  const minTranslateY = -PANEL_EXPANDED_HEIGHT + PANEL_COLLAPSED_HEIGHT
  const maxTranslateY = 0

  panelTranslateY.value = Math.max(minTranslateY, Math.min(maxTranslateY, newTranslateY))
}

const handleMouseUp = () => {
  if (!isDragging.value) return
  isDragging.value = false

  // 根据拖拽距离决定展开或收起
  const threshold = -DRAG_THRESHOLD
  if (panelTranslateY.value < threshold) {
    isExpanded.value = true
  } else {
    isExpanded.value = false
  }
  updatePanelPosition()

  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
}

// 过滤掉不需要显示的标记
const filteredMarks = computed(() => {
  return Object.entries(marks.value)
    .filter(([key, item]) => parseFloat(key) > 5 && item.id) // 过滤掉创建和实际/计划标记
    .map(([, item]) => item)
})

const onNodeClick: any = async (node: any) => {
  if (!node.id) return
  const res = await BasicsApi.getSimpleBasics(node.basicsId)
  wsCache.set('project_page_show_form', {
    categoryId: res.categoryIds?.[0],
    basicsId: res.id,
    page: 'activities',
    stage: node.stage,
    id: node.id,
    tab: 'depend'
  })
  router.push({
    name: 'ProjectCenter',
    query: {
      v: Math.random()
    }
  })
}

//监听传入值的改变
watch(
  () => [props.process, props.keyNodeDate, props.createDate],
  () => {
    tempProcess.value = props.process
    initMarks()
  }
)
</script>

<style lang="scss" scoped>
.floating-panel-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  pointer-events: none;
}

.floating-panel {
  position: relative;
  background: #fff;
  border-radius: 16px 16px 0 0;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: auto;
  max-height: 80vh;
  overflow: hidden;
}

.panel-handle {
  padding: 12px 20px;
  cursor: pointer;
  user-select: none;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 8px;
  background: #fff;
  border-radius: 16px 16px 0 0;
  position: relative;
  z-index: 1001;
}

.handle-bar {
  width: 40px;
  height: 4px;
  background: #e0e0e0;
  border-radius: 2px;
  transition: background-color 0.2s;
}

.panel-handle:hover .handle-bar {
  background: #bdbdbd;
}

.panel-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.panel-content {
  padding: 0 20px 20px;
  max-height: calc(80vh - 60px);
  overflow-y: auto;
}

.progress-section {
  margin-bottom: 24px;
  height: 4rem;
}

.details-section {
  .progress-info {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;

    .info-item {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .label {
        font-size: 12px;
        color: #666;
      }

      .value {
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }
  }
}

.nodes-list {
  .list-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
  }

  .node-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;

    &.clickable {
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background-color: #f8f9fa;
        border-radius: 8px;
        margin: 0 -8px;
        padding: 12px 8px;
      }
    }

    &:last-child {
      border-bottom: none;
    }
  }

  .node-info {
    flex: 1;

    .node-name {
      font-size: 14px;
      color: #333;
      margin-bottom: 4px;
    }

    .node-dates {
      display: flex;
      gap: 12px;
      font-size: 12px;

      .actual-date {
        color: #666;
      }

      .plan-date {
        color: #999;
      }
    }
  }

  .node-arrow {
    color: #ccc;
    font-size: 16px;
  }
}

.panel-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 999;
  pointer-events: auto;
}

// 原有的进度条样式
.el-slider {
  height: 1rem;
}

:deep(.slider-process .el-slider__runway .el-slider__button-wrapper)::before {
  content: attr(aria-valuenow) '%';
  font-size: 1rem;
  color: #fff;
  position: absolute;
  top: 0px;
  left: -34px;
  text-shadow:
    1px 1px 0 rgba(0, 0, 0, 0.1),
    -1px -1px 0 rgba(0, 0, 0, 0.1),
    1px -1px 0 rgba(0, 0, 0, 0.1),
    -1px 1px 0 rgba(0, 0, 0, 0.1),
    2px 2px 4px rgba(0, 0, 0, 0.2);
}

:deep(.el-slider__runway) {
  height: 1rem;
  border-radius: 2px;
  background-color: #ebeff3;

  .el-slider__bar {
    height: 1rem;
    background-color: var(--el-color-success-light-3);
    border-radius: 2px;
  }

  .el-slider__button-wrapper {
    height: 1rem;
    top: 0px;
    line-height: 1rem;
    width: 1rem;
    right: 0;

    .el-slider__button {
      border-radius: 2px;
      width: 1rem;
      height: 1rem;
      border-color: var(--el-color-primary);
    }
  }
}

.completed-process {
  :deep(.el-slider__button-wrapper) {
    left: 99.5% !important;
  }
}

.process-scale {
  display: flex;
  text-align: center;
  position: relative;
  color: var(--secondary-text-color);
  font-size: 0.7rem;
  line-height: 1.2;
  margin-top: -1rem;
}

:deep(.slider__stop) {
  position: absolute;
  left: 50%;
  border-radius: 0px;
  height: 1rem;
  width: 0.15vw;
  background-color: #fff;
}

// 响应式设计
@media (max-width: 768px) {
  .floating-panel {
    border-radius: 20px 20px 0 0;
  }

  .panel-handle {
    padding: 16px 20px;
  }

  .panel-content {
    padding: 0 16px 20px;
  }

  .progress-info {
    flex-direction: column;
    gap: 12px !important;
  }
}
</style>
