<!-- RuleConfig.vue -->
<template>
  <Dialog title="规则配置" v-model="visible" width="90%">
    <vxe-split class="h-[calc(100vh-300px)]">
      <vxe-split-pane width="70%">
        <div class="p-[10px_20px]">
          <CardTitle title="规则信息" />
          <el-form :model="ruleForm" label-width="80px" size="small">
            <el-form-item label="规则编码">
              <el-input v-model="ruleForm.ruleCode" placeholder="请输入规则编码" />
            </el-form-item>

            <el-form-item label="规则名称">
              <el-input v-model="ruleForm.ruleName" placeholder="请输入规则名称" />
            </el-form-item>

            <el-form-item label="关联编码">
              <el-select v-model="ruleForm.associationId" placeholder="请选择关联编码">
                <el-option
                  v-for="item in ruleList"
                  :key="item.id"
                  :label="item.ruleName"
                  :value="item.id!"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="描述">
              <el-input v-model="ruleForm.description" type="textarea" placeholder="请输入描述" />
            </el-form-item>

            <el-divider>规则段配置</el-divider>
            <vxe-table
              :data="ruleForm.segments"
              :header-cell-config="{ height: 30 }"
              :cell-config="{ height: 30 }"
              height="300"
              align="center"
              border
              stripe
              :row-config="{ drag: true }"
              @row-dragend="drageChange"
              ref="tableRef"
            >
              <vxe-column title="序号" type="seq" width="60" drag-sort />
              <vxe-column title="编码" field="segmentCode" width="100">
                <template #default="{ row }">
                  <el-input v-model="row.segmentCode" class="no-shadow" />
                </template>
              </vxe-column>
              <vxe-column title="名称" field="segmentName" width="150">
                <template #default="{ row }">
                  <el-input v-model="row.segmentName" class="no-shadow" />
                </template>
              </vxe-column>
              <vxe-column title="类型" field="segmentType" width="100">
                <template #default="{ row }">
                  <el-select v-model="row.segmentType" class="no-shadow">
                    <el-option label="固定字符" value="FIXED" />
                    <el-option label="流水号" value="SERIAL" />
                    <el-option label="选择" value="SELECT" />
                    <el-option label="输入" value="INPUT" />
                    <el-option label="关联" value="ASSOCIATION" />
                  </el-select>
                </template>
              </vxe-column>
              <vxe-column title="规则" field="configData" min-width="200">
                <template #default="{ row }">
                  <template v-if="row.segmentType == 'INPUT'">
                    <div class="flex !h-24px">
                      <div class="flex flex-1">
                        <div>字符长度：</div>
                        <el-input
                          v-model="row.configData.strLength"
                          type="number"
                          size="small"
                          class="!w-100px"
                        />
                      </div>
                      <div class="flex flex-1">
                        <div>默认字符：</div>
                        <el-input
                          v-model="row.configData.defaultValue"
                          type="number"
                          size="small"
                          class="!w-100px"
                        />
                      </div>
                      <div class="flex flex-1">
                        <div>正则校验：</div>
                        <el-input v-model="row.configData.regular" size="small" class="!w-100px" />
                      </div>
                      <div class="flex flex-1">
                        <div>格式提醒：</div>
                        <el-input
                          v-model="row.configData.placeholder"
                          size="small"
                          class="!w-100px"
                        />
                      </div>
                    </div>
                  </template>
                  <template v-else-if="row.segmentType == 'SERIAL'">
                    <div class="flex !h-24px">
                      <div class="flex flex-1">
                        <div>字符长度：</div>
                        <el-input
                          v-model="row.configData.strLength"
                          type="number"
                          size="small"
                          class="!w-100px"
                        />
                      </div>
                      <div class="flex flex-1">
                        <div>起始值：</div>
                        <el-input
                          v-model="row.configData.startValue"
                          type="number"
                          size="small"
                          class="!w-100px"
                        />
                      </div>
                      <div class="flex flex-1">
                        <div>步长：</div>
                        <el-input
                          v-model="row.configData.step"
                          type="number"
                          size="small"
                          class="!w-100px"
                        />
                      </div>
                    </div>
                  </template>
                  <template v-else-if="row.segmentType == 'ASSOCIATION'">
                    <div class="flex !h-24px">
                      <div class="flex flex-1">
                        <div>公式：</div>
                        <el-input v-model="row.configData.formula" size="small" class="!w-80%" />
                      </div>
                    </div>
                  </template>
                  <template v-else-if="row.segmentType == 'SELECT'">
                    <div class="flex !h-24px">
                      <div class="flex flex-1">
                        <div>字典：</div>
                        <el-input v-model="row.configData.dict" size="small" class="!w-80%" />
                      </div>
                    </div>
                  </template>
                  <template v-else-if="row.segmentType == 'FIXED'">
                    <div class="flex !h-24px">
                      <div class="flex flex-1">
                        <div>占位：</div>
                        <el-input v-model="row.configData.value" size="small" class="!w-80%" />
                      </div>
                    </div>
                  </template>
                </template>
              </vxe-column>
              <vxe-column title="必填" field="required" width="60">
                <template #default="{ row }">
                  <el-checkbox v-model="row.required" />
                </template>
              </vxe-column>
              <vxe-column title="操作" field="operation" width="60" fixed="right">
                <template #default="{ rowIndex }">
                  <el-button type="danger" link @click="removeSegment(rowIndex)">移除</el-button>
                </template>
              </vxe-column>
            </vxe-table>
          </el-form>
          <div class="text-right mt-10px">
            <el-button @click="refreashRule">清空</el-button>
            <el-button type="primary" @click="addSegment">添加段</el-button>
            <el-button type="success" @click="saveRule">保存规则</el-button>
          </div>
        </div>
      </vxe-split-pane>
      <vxe-split-pane>
        <div class="p-[10px_10px] h-[calc(100vh-330px)]">
          <CardTitle title="规则列表" />
          <vxe-table
            show-overflow
            align="center"
            :data="ruleList"
            :header-cell-config="{ height: 30 }"
            :cell-config="{ height: 30 }"
            height="100%"
          >
            <vxe-column title="编号" field="id" width="80" />
            <vxe-column title="编码" field="ruleCode" width="80" />
            <vxe-column title="名称" field="ruleName" width="120" />
            <vxe-column title="描述" field="description" min-width="120" />
            <vxe-column title="状态" field="status" width="80">
              <template #defult="{ row }">
                <el-tag :type="row.status === 1 ? 'success' : 'danger'">
                  {{ row.status === 1 ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </vxe-column>
            <vxe-column title="操作" field="operation" width="120" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" link @click="editRule(row)">编辑</el-button>
                <el-button type="warning" size="small" link @click="row">
                  {{ row.status ? '禁用' : '启用' }}
                </el-button>
              </template>
            </vxe-column>
          </vxe-table>
        </div>
      </vxe-split-pane>
    </vxe-split>
  </Dialog>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import * as RuleConfigApi from '@/api/project/diagram/rule'
import { cloneDeep } from 'lodash-es'

const visible = ref(false)
const ruleList = ref<RuleConfigApi.RuleConfigVO[]>([])
const tableRef = ref()

const ruleForm = reactive<RuleConfigApi.RuleConfigVO>({
  ruleCode: '',
  ruleName: '',
  description: '',
  status: 1,
  segments: []
})

const addSegment = () => {
  ruleForm.segments.push({
    segmentCode: '',
    segmentName: '',
    segmentType: 'INPUT',
    segmentOrder: ruleForm.segments.length + 1,
    configData: {},
    required: true
  })
}

const removeSegment = (index: number) => {
  ruleForm.segments.splice(index, 1)
}

const saveRule = async () => {
  try {
    const data = cloneDeep(ruleForm)
    let order = 1
    data.segments.forEach((item) => {
      item.configData = JSON.stringify(item.configData)
      item.segmentOrder = order++
    })
    await RuleConfigApi.saveRule(data)
    refreashRule()
    onListRule()
    ElMessage.success('保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

const onListRule = async () => {
  ruleList.value = await RuleConfigApi.listRules()
}

const openForm = () => {
  visible.value = true
  onListRule()
}

const editRule = (row: RuleConfigApi.RuleConfigVO) => {
  const tempRuleForm = cloneDeep(row)
  tempRuleForm.segments.forEach((item) => {
    item.configData = JSON.parse(item.configData)
  })
  Object.assign(ruleForm, tempRuleForm)
}

const drageChange = () => {
  const $table = tableRef.value
  if ($table) {
    const tableData = $table.getTableData().tableData
    let i = 0
    tableData.forEach((element) => {
      element.segmentOrder = i++
    })
  }
}

const refreashRule = () => {
  Object.assign(ruleForm, {
    id: undefined,
    associationId: undefined,
    ruleCode: '',
    ruleName: '',
    description: '',
    status: 1,
    segments: []
  })
}

defineExpose({
  openForm
})
</script>

<style scoped lang="scss">
.segment-item {
  margin-bottom: 20px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.no-shadow {
  :deep(.el-input__wrapper),
  :deep(.el-select__wrapper) {
    box-shadow: none;
  }
}
</style>
