// api/number.ts
import request from '@/config/axios'
// types/number.ts
export interface GenerateNumberVO {
  ruleCode: string
  segmentValues: Record<string, any>
  description: string
}

export interface NumberRecordVO {
  id: number
  parentId?: number
  ruleCode: string
  fullNumber: string
  segmentValues: Record<string, any>
  recordType: string
  creator: string
  createTime: string
}

export const generatePrincipleNumber = (data: GenerateNumberVO) => {
  return request.post<NumberRecordVO>({ url: '/project/diagram/principle', data })
}

export const generateChildNumber = (parentId: number, data: GenerateNumberVO) => {
  return request.post<NumberRecordVO>({ url: `/project/diagram/child/${parentId}`, data })
}

export const getChildren = (parentId: number, type: string) => {
  return request.get<NumberRecordVO[]>({ url: `/project/diagram/children/${parentId}/${type}` })
}

export const getRecord = (id: number) => {
  return request.get<NumberRecordVO>({ url: `/project/diagram/${id}` })
}

export const getPage = (params: any) => {
  return request.get({ url: `/project/diagram/page`, params })
}

export const updateDescription = (id: number, description: string) => {
  return request.post({ url: `/project/diagram/update-description/${id}`, data: description })
}

export const getLastNumber = (parentId: number, ruleCode: string, type: string) => {
  return request.get({ url: `/project/diagram/get-last-number`, params: { parentId, ruleCode, type } })
}
