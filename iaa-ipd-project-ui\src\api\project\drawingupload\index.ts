import request from '@/config/axios'

export const DrawingUploadApi = {
  /** 获取上传看板分页 */
  getDrawingUploadPage: async (params: any) => {
    return await request.post({ url: `/project/drawing-upload/page`, data: params })
  },
  /** 获取上传看板列表 */
  getDrawingUploadList: async () => {
    return await request.post({ url: `/project/drawing-upload/list` })
  },
  /** 保存上传看板 */
  addDrawingUpload: async (params: any) => {
    return await request.post({ url: `/project/drawing-upload/add`, data: params })
  },
  /** 导出上传看板分页 */
  exportDrawingUpload: (data: any) => {
    return request.downloadPost({ url: '/project/drawing-upload/export-drawing-page', data })
  }
}
