<template>
  <el-collapse-item
    title="输出参考"
    name="1"
    v-if="
      (formData.mold == 0 && formData.targetType == 0) ||
      (formData?.targetTemplateIds || [])?.length > 0
    "
  >
    <div class="flex">
      <div
        class="cursor-pointer w-70px h-120px p-5px hover:bg-#f8f8f8"
        v-for="file in fileTemplateList.filter((item) =>
          formData.targetTemplateIds?.includes(item.id)
        )"
        :key="file.id"
        @click="officeEditorRef.open(file.infraFileId, file.name)"
      >
        <img :src="getImagePath(file.uri, file.hasFolder)" class="w-60px h-60px" />
        <div
          class="line-clamp-2 overflow-hidden [display:-webkit-box] [-webkit-box-orient:vertical] [-webkit-line-clamp:2] h-40px text-.65vw word-break-normal"
        >
          {{ file.name }}
        </div>
      </div>
    </div>
  </el-collapse-item>
  <el-collapse-item title="附件输出" name="2">
    <el-button
      type="primary"
      plain
      class="!w-full mb-10px"
      size="small"
      @click="activitiesQuoteFileRef?.openForm()"
      v-if="
        allowPermission &&
        data.status !== 10 &&
        data.progress !== 100 &&
        allowTheOutput &&
        (formData?.workHoursType != '99' ? formData?.workHoursAudit : true)
      "
    >
      引用文件
    </el-button>
    <vxe-table
      class="w-100%"
      :header-cell-style="{ padding: '5px', backgroundColor: '#fff' }"
      :cell-style="{ padding: '5px' }"
      :header-cell-config="{ height: 24 }"
      :cell-config="{ height: 24 }"
      show-overflow
      :data="attachmentList"
      align="center"
      border
    >
      <vxe-column title="文件名" field="name" min-width="200" align="left">
        <template #default="{ row }">
          <el-link
            type="primary"
            @click="attachmentPreviewRef?.openForm(row.name, row.processInstanceId)"
          >
            {{ row.name }}
          </el-link>
        </template>
      </vxe-column>
      <vxe-column title="版本" field="currentVersion" width="60" />
      <vxe-column title="绑定参考" field="templateId" width="200">
        <template #default="{ row }">
          <el-select v-model="row.templateId" :disabled="true">
            <el-option
              v-for="item in fileTemplateList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </template>
      </vxe-column>
      <vxe-column title="审签状态" field="approvalStatus" width="90">
        <template #default="{ row }">
          <DictTag type="project_target_approval_status" :value="row.approvalStatus" />
        </template>
      </vxe-column>
      <vxe-column
        title="审核通过时间"
        field="approvalTime"
        :formatter="dateFormatter3"
        width="120"
      />
      <vxe-column title="操作" width="120px" fixed="right" align="center">
        <template #default="{ row }">
          <el-button
            type="primary"
            link
            size="small"
            v-if="![0].includes(row.approvalStatus) && row.creator === getUser.id"
            @click="fileUploadAnewRef?.openForm(row)"
          >
            重传
          </el-button>
          <el-button
            type="primary"
            link
            size="small"
            v-if="row.approvalStatus != 1 && row.creator === getUser.id"
            @click="delAttachment(row)"
          >
            删除
          </el-button>
        </template>
      </vxe-column>
    </vxe-table>
  </el-collapse-item>
  <el-collapse-item title="未通过审批的输出物" name="3">
    <vxe-table
      class="w-100%"
      :header-cell-style="{ padding: '5px', backgroundColor: '#fff' }"
      :cell-style="{ padding: '5px' }"
      :header-cell-config="{ height: 24 }"
      :cell-config="{ height: 24 }"
      show-overflow
      :data="notApprovalAttachmentList"
      align="center"
      border
    >
      <vxe-column title="文件名" field="name" min-width="200" align="left">
        <template #default="{ row }">
          <el-link
            type="primary"
            @click="attachmentPreviewRef?.openForm(row.name, row.processInstanceId)"
          >
            {{ row.name }}
          </el-link>
        </template>
      </vxe-column>
      <vxe-column title="版本" field="currentVersion" width="60" />
      <vxe-column title="绑定参考" field="templateId" width="200">
        <template #default="{ row }">
          <el-select v-model="row.templateId" :disabled="true">
            <el-option
              v-for="item in fileTemplateList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </template>
      </vxe-column>
      <vxe-column title="审签状态" field="approvalStatus" width="90">
        <template #default="{ row }">
          <DictTag type="project_target_approval_status" :value="row.approvalStatus" />
        </template>
      </vxe-column>
    </vxe-table>
  </el-collapse-item>

  <!-- 组件引用 -->
  <FileUploadAnew
    category="activities"
    :dynamic-id="formData.id!"
    ref="fileUploadAnewRef"
    :file-template-list="
      fileTemplateList.filter((item) => formData.targetTemplateIds?.includes(item.id))
    "
    :target-type="formData.targetType"
    :target-docking-id="formData.targetDockingId"
    @success="onListAttachment"
  />
  <OfficeEditor ref="officeEditorRef" :download="true" />
  <AttachmentPreview ref="attachmentPreviewRef" />
  <ActivitiesQuoteFile
    :basics-id="data?.basicsId"
    :file-template-list="fileTemplateList || []"
    :target-template-ids="data?.targetTemplateIds || []"
    :activities-id="data?.id"
    ref="activitiesQuoteFileRef"
    @view="attachmentPreviewRef?.openForm"
    @success="onListAttachment"
  />
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { ActivitiesVO } from '@/api/project/activities'
import { AttachmentApi, AttachmentRespVO } from '@/api/project/attachment'
import { useUserStore } from '@/store/modules/user'
import { useMessage } from '@/hooks/web/useMessage'
import { ElMessageBox } from 'element-plus'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import { getImagePath } from '@/utils/icon'
import { dateFormatter3 } from '@/utils/formatTime'
import FileUploadAnew from '../../components/FileUploadAnew.vue'
import AttachmentPreview from '../../components/AttachmentPreview.vue'
import ActivitiesQuoteFile from './ActivitiesQuoteFile.vue'

const props = defineProps<{
  data: ActivitiesVO
  formData: ActivitiesVO
  fileTemplateList: any[]
  allowPermission: boolean
  allowTheOutput: boolean
}>()

const emits = defineEmits(['attachment-updated'])

const { getUser } = useUserStore()
const message = useMessage()

const attachmentList = ref<AttachmentRespVO[]>([])
const notApprovalAttachmentList = ref<AttachmentRespVO[]>([])

// 组件引用
const collapseRef = ref()
const officeEditorRef = ref()
const fileUploadAnewRef = ref()
const attachmentPreviewRef = ref()
const activitiesQuoteFileRef = ref()

// 方法
const onListAttachment = async () => {
  const res = await AttachmentApi.getAttachmentList({
    category: 'activities',
    dynamicId: props.formData.id
  })
  attachmentList.value = res
  emits('attachment-updated', res)
}

const onListNotApprovalAttachment = async () => {
  const res = await AttachmentApi.getNotApprovalAttachmentList('activities' + props.formData.id)
  notApprovalAttachmentList.value = res
}

const delAttachment = async (row: any) => {
  const { value } = await ElMessageBox.prompt('请输入删除原因', '删除输出物', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/,
    inputErrorMessage: '删除原因不能为空'
  })
  if (row.approvalStatus == 2) {
    await AttachmentApi.delAttachment({
      id: row.id,
      value
    })
  } else {
    await ProcessInstanceApi.cancelProcessInstanceByStartUser(row.processInstanceId, value)
  }
  message.success('删除成功')
  onListAttachment()
}

// 初始化方法，由父组件调用
const init = async () => {
  if (props.formData?.id) {
    await onListAttachment()
    onListNotApprovalAttachment()
  }
}

// 暴露方法给父组件
defineExpose({
  init,
  onListAttachment
})
</script>

<style lang="scss" scoped>
.custom-collapse {
  :deep(.el-collapse-item__header) {
    background-color: #fafafa !important;
    border: 0.3px dashed var(--el-color-info-light-5) !important;
    border-left: 5px solid var(--el-color-primary) !important;
    font-size: 1rem;
    height: 1.8rem;
  }
}
</style>
