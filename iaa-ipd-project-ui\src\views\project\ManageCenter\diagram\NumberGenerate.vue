<!-- NumberGenerate.vue -->
<template>
  <ContentWrap>
    <vxe-split class="h-[calc(100vh-160px)]">
      <vxe-split-pane width="70%">
        <div class="p-[10px_20px]">
          <div class="flex justify-between">
            <CardTitle title="原理图号列表" />
            <el-button
              size="small"
              type="primary"
              plain
              @click="ruleConfigRef?.openForm()"
              v-hasPermi="['project:diagram-rule-config:save']"
            >
              规则设置
            </el-button>
          </div>
          <div class="mt-10px">
            <el-button
              size="small"
              type="primary"
              plain
              @click="generateCodeRef?.openForm('electronic')"
            >
              申请新的原理图号
            </el-button>
            <div class="mt-10px h-[calc(100vh-285px)]">
              <vxe-table
                :header-cell-config="{ height: 34 }"
                :cell-config="{ height: 34 }"
                :row-config="{ isHover: true, isCurrent: true }"
                :row-style="{ cursor: 'pointer' }"
                height="100%"
                :data="numberList"
                :filter-config="{ remote: true }"
                align="center"
                show-overflow
                border
                :loading="loading"
                @current-row-change="onCurrentRowChange"
                @filter-change="handleFilterChange"
                :tree-config="{
                  rowField: 'id',
                  parentField: 'parentId',
                  lazy: true,
                  hasChild: 'hasChild',
                  showLine: true,
                  loadMethod: ({ row }) => {
                    return onLoadChildList(row)
                  }
                }"
              >
                <vxe-column
                  title="图号"
                  field="fullNumber"
                  width="240"
                  :filters="filterValue.fullNumber"
                  :filter-render="FilterTemplate.textFilterRender"
                  tree-node
                />
                <vxe-column title="类型" field="recordType" width="100" :filters="statusOptions">
                  <template #default="{ row }">
                    <el-tag :type="getRecordTypeTag(row.recordType)">
                      {{ getRecordTypeText(row.recordType) }}
                    </el-tag>
                  </template>
                </vxe-column>
                <vxe-column
                  title="描述"
                  field="description"
                  min-width="150"
                  :filters="filterValue.description"
                  :filter-render="FilterTemplate.textFilterRender"
                />
                <vxe-column
                  title="创建人"
                  field="creator"
                  width="100"
                  :filters="filterValue.creator"
                  :filter-render="FilterTemplate.textFilterRender"
                />
                <vxe-column
                  title="创建时间"
                  field="createTime"
                  :formatter="dateFormatter3"
                  width="160"
                />
                <vxe-column
                  title="操作"
                  field="operation"
                  width="150px"
                  fixed="right"
                  align="center"
                >
                  <template #default="{ row }">
                    <el-button type="primary" link @click.stop="handleCopyCode(row.fullNumber)">
                      复制
                    </el-button>
                    <el-dropdown @command="(command) => handleCommand(command, row)">
                      <el-button type="primary" link>
                        <Icon icon="ep:d-arrow-right" /> 更多
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item command="remark" v-if="getUser.nickname == row.creator">
                            修改备注
                          </el-dropdown-item>
                          <el-dropdown-item command="principle" v-if="row.parentId == 0">
                            生成子原理图号
                          </el-dropdown-item>
                          <el-dropdown-item command="part" v-if="row.ruleCode == 'electronic'">
                            生成子配件板图号
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                    <!-- <el-button
                      type="warning"
                      link
                      @click.stop="handleUpdateRemark(row, true)"
                      v-if="getUser.nickname == row.creator"
                    >
                      修改备注
                    </el-button>
                    <el-button
                      size="small"
                      type="success"
                      link
                      @click.stop="generateCodeRef?.openForm('electronic', row)"
                      v-if="row.parentId == 0"
                    >
                      生成子原理图号
                    </el-button>
                    <el-button
                      size="small"
                      type="danger"
                      link
                      v-if="row.parentId == 0"
                      @click="generateCodeRef?.openForm('accessory', currentRow)"
                    >
                      生成子配件板图号
                    </el-button> -->
                  </template>
                </vxe-column>
              </vxe-table>
            </div>
            <Pagination
              :total="total"
              v-model:page="queryParams.pageNo"
              v-model:limit="queryParams.pageSize"
              @pagination="onList"
            />
          </div>
        </div>
      </vxe-split-pane>
      <vxe-split-pane>
        <div class="p-[10px_20px]">
          <CardTitle title="关联图号列表" />
          <el-empty description="请选择左侧基础原理图号" v-if="!currentRow?.id" />
          <template v-else>
            <div class="mt-10px flex flex-wrap gap-10px operation">
              <el-button
                size="small"
                type="warning"
                plain
                @click="generateCodeRef?.openForm('bom', currentRow)"
              >
                生成BOM图号
              </el-button>
              <el-button
                size="small"
                type="info"
                plain
                @click="generateCodeRef?.openForm('source', currentRow)"
              >
                生成源代码\烧录码图号
              </el-button>
            </div>
            <div class="h-[calc(100vh-300px)] bg-#f1f1f1 mt-10px">
              <vxe-table
                :data="numberChildList"
                :header-cell-config="{ height: 34 }"
                :cell-config="{ height: 34 }"
                height="100%"
                align="center"
                border
                show-overflow
                :loading="loading"
                :virtual-y-config="{ enabled: true, gt: 0 }"
              >
                <vxe-column
                  title="图号"
                  field="fullNumber"
                  width="200"
                  :filters="childFilterValue.fullNumber"
                  :filter-render="FilterTemplate.textFilterRender"
                />
                <vxe-column title="类型" field="recordType" width="100" :filters="statusOptions">
                  <template #default="{ row }">
                    <el-tag :type="getRecordTypeTag(row.recordType)">
                      {{ getRecordTypeText(row.recordType) }}
                    </el-tag>
                  </template>
                </vxe-column>
                <vxe-column
                  title="描述"
                  field="description"
                  min-width="150"
                  :filters="childFilterValue.description"
                  :filter-render="FilterTemplate.textFilterRender"
                />
                <vxe-column
                  title="创建人"
                  field="creator"
                  width="100"
                  :filters="childFilterValue.creator"
                  :filter-render="FilterTemplate.textFilterRender"
                />
                <vxe-column
                  title="创建时间"
                  field="createTime"
                  :formatter="dateFormatter3"
                  width="160"
                />
                <vxe-column
                  title="操作"
                  field="operation"
                  width="140px"
                  fixed="right"
                  align="center"
                >
                  <template #default="{ row }">
                    <el-button type="primary" link @click="handleCopyCode(row.fullNumber)">
                      复制
                    </el-button>
                    <el-button
                      type="warning"
                      link
                      @click="handleUpdateRemark(row, false)"
                      v-if="getUser.nickname == row.creator"
                    >
                      修改备注
                    </el-button>
                  </template>
                </vxe-column>
              </vxe-table>
            </div>
          </template>
        </div>
      </vxe-split-pane>
    </vxe-split>
  </ContentWrap>
  <RuleConfig ref="ruleConfigRef" />
  <GenerateCode ref="generateCodeRef" @success="getChildList" />
</template>

<script setup lang="ts">
import * as NumberApi from '@/api/project/diagram/number'
import RuleConfig from './RuleConfig.vue'
import GenerateCode from './GenerateCode.vue'
import { dateFormatter3 } from '@/utils/formatTime'
import { handleCopyCode } from '@/utils'
import * as FilterTemplate from '@/views/project/ProjectCenter/main/components/Filter'
import { VxeColumnPropTypes } from 'vxe-table'
import { useUserStore } from '@/store/modules/user'

const { getUser } = useUserStore()

const filterValue = reactive({
  fullNumber: [{ data: '' }],
  description: [{ data: '' }],
  creator: [{ data: '' }]
})

const childFilterValue = reactive({
  fullNumber: [{ data: '' }],
  description: [{ data: '' }],
  creator: [{ data: '' }]
})

const statusOptions = ref<VxeColumnPropTypes.Filters>([
  { label: '原理图', value: 'PRINCIPLE' },
  { label: 'BOM表', value: 'BOM' },
  { label: '源代码', value: 'SOURCE' },
  { label: '配件板', value: 'PART' }
])

const ruleConfigRef = ref()
const generateCodeRef = ref()

const numberList = ref<any[]>([])
const numberChildList = ref<any[]>([])
const loading = ref(false)
const total = ref(0)
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  recordType: 'PRINCIPLE',
  fullNumber: undefined,
  description: undefined,
  creator: undefined
})
const currentRow = ref<any>({})

const onList = async () => {
  loading.value = true
  try {
    const res = await NumberApi.getPage(queryParams.value)
    numberList.value = res.list
    numberList.value.forEach((item) => {
      item.hasChild = item.childCount > 0
    })
    total.value = res.total
  } finally {
    loading.value = false
  }
}

const handleList = () => {
  queryParams.value.pageNo = 1
  onList()
}

const handleFilterChange = (params: any) => {
  // 特定字段列表
  const specialFields = ['']

  // 初始化 filters 对象
  const filters = {}

  params.filterList.forEach((item: any) => {
    const { field, values, datas } = item

    if (specialFields.includes(field) && values.length > 0) {
      // 特定字段且 values 有值
      filters[field] = values
    } else if (datas.length > 0) {
      // 其他字段且 datas 有值
      filters[field] = datas[0]
    }
  })

  // 清空不在 params.filterList 中的字段
  Object.keys(queryParams.value).forEach((key) => {
    if (
      !['pageNo', 'pageSize'].includes(key) &&
      !params.filterList.some((item: any) => item.field === key)
    ) {
      queryParams.value[key] = undefined
    }
  })
  // 更新 queryParams
  Object.assign(queryParams.value, filters)

  // 调用后端接口获取数据
  handleList()
}

const handleUpdateRemark = async (row: any, parent: boolean) => {
  const { value } = await ElMessageBox.prompt(`请输入编码${row.fullNumber}的新备注`, {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputValue: row.description,
    inputErrorMessage: '备注不能为空'
  })
  await NumberApi.updateDescription(row.id, value)
  ElMessage.success('修改成功')
  if (parent) {
    onList()
  } else {
    getChildList('')
  }
}

const handleCommand = (command: string, row: any) => {
  switch (command) {
    case 'remark':
      handleUpdateRemark(row, true)
      break
    case 'principle':
      generateCodeRef.value?.openForm('electronic', row)
      break
    case 'part':
      generateCodeRef.value?.openForm('accessory', row)
      break
  }
}

const onCurrentRowChange = async ({ row }) => {
  currentRow.value = row
  getChildList('')
}

const getChildList = async (type: string) => {
  if (type == 'electronic' || type == 'accessory') {
    onList()
    return
  }
  if (!currentRow.value.id) return
  numberChildList.value = await NumberApi.getChildren(currentRow.value.id, 'OTHER')
}

const onLoadChildList = async (row: any) => {
  row.children = await NumberApi.getChildren(row.id, 'PRINCIPLE')
  consol
  return row.children
}
const getRecordTypeTag: any = (type: string) => {
  const typeMap: Record<string, string> = {
    PRINCIPLE: 'success',
    BOM: 'warning',
    SOURCE: 'info',
    PART: 'danger'
  }
  return typeMap[type] || 'info'
}

const getRecordTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    PRINCIPLE: '原理图',
    BOM: 'BOM表',
    SOURCE: '源代码',
    PART: '配件板'
  }
  return typeMap[type] || type
}

onMounted(() => {
  onList()
})
</script>

<style scoped>
.record-card {
  margin-top: 20px;
}
:deep(.vxe-table--render-default .vxe-body--row.row--current > .vxe-body--column) {
  background-color: #ffdbab !important;
}

.operation {
  .el-button + .el-button {
    margin-left: 0 !important;
  }
}
</style>
