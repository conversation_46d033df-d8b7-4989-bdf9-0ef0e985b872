<template>
  <div class="p-l-10px h-full overflow-auto" ref="containerRef">
    <el-card shadow="never">
      <template #header>
        <!-- <vxe-toolbar size="mini" ref="toolbarRef" custom export /> -->
        <div class="flex justify-between items-center">
          <el-button type="primary" @click="saveAll">保存</el-button>
          <vxe-toolbar size="mini" ref="toolbarRef" custom export />
        </div>
      </template>

      <!-- 列表 -->
      <div class="h-[calc(100vh-150px)]">
        <div class="h-[calc(100%-50px)]">
          <vxe-table
            height="100%"
            ref="tableRef"
            :data="list"
            border
            stripe
            show-overflow
            :header-cell-config="{ height: 30 }"
            :column-config="{ resizable: true, isHover: true }"
            :cell-config="{ height: 36 }"
            :row-config="{ isHover: true, isCurrent: true }"
            :loading="loading"
            align="center"
            @filter-change="handleFilterChange"
            :filter-config="{ remote: true }"
            :edit-config="{ trigger: 'click', mode: 'row', showStatus: true }"
            :keep-source="true"
            :export-config="{ remote: true, message: false, exportMethod: onExport }"
          >
            <vxe-column
              title="物料编码"
              field="materialCode"
              width="120"
              :filters="filterValue.materialCode"
              :filter-render="FilterTemplate.textFilterRender"
              align="left"
            />
            <vxe-column
              title="物料版本"
              field="materialVersion"
              width="90"
              :filters="filterValue.materialVersion"
              :filter-render="FilterTemplate.textFilterRender"
            />
            <vxe-column
              title="物料名称"
              field="materialName"
              width="150"
              align="left"
              :filters="filterValue.materialName"
              :filter-render="FilterTemplate.textFilterRender"
            />
            <vxe-column
              title="规格型号"
              field="specifications"
              width="300"
              align="left"
              :filters="filterValue.specifications"
              :filter-render="FilterTemplate.textFilterRender"
            />
            <vxe-column
              title="计划完成时间"
              field="plannedCompletionDate"
              width="140"
              :edit-render="{
                name: '$input',
                props: {
                  type: 'date',
                  format: 'yyyy-MM-dd',
                  valueFormat: 'yyyy-MM-dd'
                }
              }"
            />
            <vxe-column title="实际完成时间" field="actualCompletionDate" width="140" />
            <vxe-column
              title="备注"
              field="remark"
              min-width="110"
              :edit-render="{ name: 'input' }"
            />
            <vxe-column
              title="物料创建人"
              field="createName"
              width="120"
              :filters="filterValue.createName"
              :filter-render="FilterTemplate.textFilterRender"
            />
            <vxe-column title="物料创建日期" field="createTime" width="150">
              <template #default="{ row }">
                {{ formatToDateTime(row.createTime) }}
              </template>
            </vxe-column>
            <vxe-column
              title="物料修改人"
              field="updateName"
              width="120"
              :filters="filterValue.updateName"
              :filter-render="FilterTemplate.textFilterRender"
            />
            <vxe-column title="物料修改日期" field="updateTime" width="150">
              <template #default="{ row }">
                {{ formatToDateTime(row.updateTime) }}
              </template>
            </vxe-column>
            <vxe-column title="文档名称" field="docName" width="150" />
            <vxe-column title="规格书" field="specificationList" width="150">
              <template #default="{ row }">
                <el-dropdown @command="handleCommand" v-if="row.specificationList?.length > 0">
                  <el-button type="primary" link class="el-dropdown-link">
                    {{ row.specificationList[0].name }}
                    <el-icon class="el-icon--right"><arrow-down /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item
                        v-for="item in row.specificationList"
                        :key="item.id"
                        :command="item"
                      >
                        {{ item.name }}
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </vxe-column>
          </vxe-table>
        </div>
        <Pagination
          v-model:limit="queryParams.pageSize"
          v-model:page="queryParams.pageNo"
          :total="total"
          size="small"
          @pagination="getDrawingUploadPage"
        />
      </div>
    </el-card>
    <OfficeEditor ref="officeEditorRef" has-dialog :download="true" />
    <NoModalDrawer v-model="specificationDrawerVisible" size="60%">
      <template #header>
        <div class="flex justify-between items-center">
          <div class="text-white font-bold text-17px">
            {{ rowData?.name }}
          </div>
          <el-button link v-if="rowData?.id" @click="specificationRef?.onExport()">
            <Icon icon="ep:download" />
            <span class="text-white">下载为word文件</span>
          </el-button>
        </div>
      </template>
      <SpecificationForm ref="specificationRef" @cancel="specificationDrawerVisible = false" />
    </NoModalDrawer>
  </div>
</template>

<script setup lang="ts">
import { formatToDateTime } from '@/utils/dateUtil'
import { DrawingUploadApi } from '@/api/project/drawingupload/index'
import * as FilterTemplate from '@/views/project/ProjectCenter/main/components/Filter'
import download from '@/utils/download'
import SpecificationForm from '@/views/project/ArchiveCenter/specification/SpecificationForm.vue'

const filterValue = reactive({
  materialName: [{ data: '' }],
  materialCode: [{ data: '' }],
  materialVersion: [{ data: '' }],
  specifications: [{ data: '' }],
  createName: [{ data: '' }],
  createTime: [{ data: {} }],
  updateName: [{ data: '' }],
  updateTime: [{ data: {} }]
})

const queryParams = ref({
  pageNo: 1,
  pageSize: 100
})

const total = ref(0)
const list = ref<any>([])
const toolbarRef = ref()
const tableRef = ref()
const loading = ref(false)
const officeEditorRef = ref()
const handleFilterChange = (params: any) => {
  // 特定字段列表
  const specialFields = ['']

  // 初始化 filters 对象
  const filters = {}

  params.filterList.forEach((item: any) => {
    const { field, values, datas } = item

    if (specialFields.includes(field) && values.length > 0) {
      // 特定字段且 values 有值
      filters[field] = values
    } else if (datas.length > 0) {
      // 其他字段且 datas 有值
      filters[field] = datas[0]
    }
  })

  // 清空不在 params.filterList 中的字段
  Object.keys(queryParams.value).forEach((key) => {
    if (
      !['pageNo', 'pageSize'].includes(key) &&
      !params.filterList.some((item: any) => item.field === key)
    ) {
      queryParams.value[key] = undefined
    }
  })
  // 更新 queryParams
  Object.assign(queryParams.value, filters)

  // 调用后端接口获取数据
  getDrawingUploadPage()
}
const getDrawingUploadPage = async () => {
  try {
    const data = await DrawingUploadApi.getDrawingUploadPage(queryParams.value)
    list.value = data.list
    total.value = data.total
  } catch (error) {
    console.log(error)
  }
}
const specificationDrawerVisible = ref(false)
const rowData = ref<any>({})
const specificationRef = ref()
const handleCommand = async (command: any) => {
  loading.value = true
  try {
    rowData.value = command
    specificationDrawerVisible.value = true
    await nextTick()
    specificationRef.value?.openForm({}, true, {
      templateContent: command.formJson,
      data: command.data
    })
  } finally {
    loading.value = false
  }
}

const message = useMessage()
// 保存所有修改的数据
const saveAll = async () => {
  try {
    const $table = tableRef.value
    console.log($table)
    if ($table) {
      const updateRecords = $table.getUpdateRecords()
      console.log(updateRecords)
      if (updateRecords.length === 0) {
        message.warning('没有需要保存的数据')
        return
      }

      loading.value = true
      await DrawingUploadApi.addDrawingUpload(updateRecords)
      message.success('保存成功')

      // 重新加载数据
      await getDrawingUploadPage()
    }
  } catch (error) {
    console.error(error)
    message.success('保存失败')
  } finally {
    loading.value = false
  }
}

const onExport = async ({ options }: any) => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    const columns = options.columns.map((item) => item.field)
    if (columns.length === 0) {
      message.warning('未选择需要导出的列')
      return
    }
    if (columns.includes('date')) {
      columns.splice(columns.indexOf('date'), 1, 'startDate', 'endDate')
    }
    const data = await DrawingUploadApi.exportDrawingUpload({
      ...queryParams.value,
      columns
    })
    download.excel(data, `${options.filename}.xlsx`)
  } catch {}
}
onMounted(() => {
  getDrawingUploadPage()
  nextTick(() => {
    unref(tableRef)?.connect(unref(toolbarRef))
  })
})
</script>

<style lang="scss" scoped>
:deep(.vxe-cell) {
  padding: 0 5px !important;
}

:deep(.col--edit) {
  cursor: pointer;
}
:deep(.el-card__header) {
  padding: 0 5px !important;
}

:deep(.basics-info),
:deep(.back-1) {
  background-color: rgb(241, 241, 241);
}

:deep(.vxe-header--column .vxe-cell--title) {
  font-weight: 500;
  color: #000;
}

:deep(.vxe-cell--title .el-select__wrapper) {
  box-shadow: none !important;
  background-color: #f8f8f9;
  font-size: 14px;

  .el-select__placeholder {
    color: #000 !important;
    text-align: center;
  }
}
</style>
