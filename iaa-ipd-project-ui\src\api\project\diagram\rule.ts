import request from '@/config/axios'

// types/rule.ts
export interface RuleConfigVO {
  id?: number
  associationId?:number
  ruleCode: string
  ruleName: string
  description?: string
  status: number
  segments: RuleSegmentVO[]
}

export interface RuleSegmentVO {
  id?: number
  segmentCode: string
  segmentName: string
  segmentType: 'FIXED' | 'SERIAL' | 'SELECT' | 'INPUT' | 'ASSOCIATION'
  segmentOrder: number
  configData: any
  required: boolean
}

export const getRule = (ruleCode: string) => {
  return request.get<RuleConfigVO>({
    url: `/project/diagram-rule-config/get`,
    params: { ruleCode }
  })
}

export const saveRule = (data: RuleConfigVO) => {
  return request.post({ url: '/project/diagram-rule-config/save', data })
}

export const listRules = () => {
  return request.get<RuleConfigVO[]>({ url: '/project/diagram-rule-config/list' })
}
