<template>
  <Dialog title="生成图号" v-model="visible" width="40%" :before-close="onBeforeClose">
    <el-form
      ref="formRef"
      label-width="100"
      size="small"
      :model="formData.segmentValues"
      :rules="formRules"
    >
      <el-form-item
        v-for="item in rule?.segments"
        :key="item.id"
        :label="item.segmentName"
        :prop="!['SERIAL', 'FIXED'].includes(item.segmentType) ? item.segmentCode : ''"
      >
        <template v-if="item.segmentType == 'INPUT'">
          <el-input
            v-model="formData.segmentValues[item.segmentCode]"
            :placeholder="item.configData.placeholder"
            show-word-limit
            :maxlength="item.configData.strLength"
            :disabled="
              (formData.ruleCode == 'electronic' &&
                parentRow?.id &&
                !['hardware'].includes(item.segmentCode)) ||
              item.configData.defaultValue ||
              (formData.ruleCode == 'bom' &&
                parentRow?.id &&
                ['distinction'].includes(item.segmentCode)) ||
              (formData.ruleCode == 'accessory' &&
                parentRow?.id &&
                formData.addVersion &&
                ['distinction'].includes(item.segmentCode))
            "
          />
        </template>
        <template v-else-if="item.segmentType == 'FIXED'">
          {{ item.configData.value }}
        </template>
        <template v-else-if="item.segmentType == 'ASSOCIATION'">
          {{ formData.segmentValues[item.segmentCode] }}
        </template>
        <template v-else-if="item.segmentType == 'SERIAL'">
          {{ formData.segmentValues[item.segmentCode] || '系统生成' }}
        </template>
        <template v-else-if="item.segmentType == 'SELECT'">
          <el-select
            v-model="formData.segmentValues[item.segmentCode]"
            :disabled="
              (formData.ruleCode == 'electronic' &&
                parentRow?.id &&
                item.segmentCode !== 'coexistence') ||
              (formData.ruleCode == 'accessory' &&
                parentRow?.id &&
                formData.addVersion &&
                ['burn'].includes(item.segmentCode))
            "
          >
            <el-option
              v-for="dict in JSON.parse(item.configData.dict)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </template>
      </el-form-item>
      <el-form-item label="描述/备注">
        <template #label>
          <span class="color-red">*</span>
          描述/备注
        </template>
        <el-input type="textarea" v-model="formData.description" />
      </el-form-item>
    </el-form>

    <div v-if="result?.id">
      <div class="text-24px p-10px text-center">{{ result.fullNumber }}</div>
      <div class="p-10px text-center">
        <el-button type="primary" plain size="small" @click="handleCopyCode(result.fullNumber)">
          一键复制
        </el-button>
      </div>
    </div>
    <template #footer>
      <el-button type="primary" :loading="loading" @click="generateCode">生成</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import * as RuleConfigApi from '@/api/project/diagram/rule'
import * as NumberApi from '@/api/project/diagram/number'
import { handleCopyCode } from '@/utils'

const loading = ref(false)
const visible = ref(false)
const rule = ref<RuleConfigApi.RuleConfigVO>()
const formData = ref<NumberApi.GenerateNumberVO>({
  ruleCode: '',
  segmentValues: {},
  description: '',
  addVersion: false
})
const formRules = ref<any>({})
const result = ref<any>({})
const message = useMessage()
const formRef = ref()

const initializeFormData = () => {
  formData.value.ruleCode = rule.value!.ruleCode as any
  formData.value.segmentValues = {}
  formRules.value = {}
}
const parentRow = ref<any>({})
const emits = defineEmits(['success'])

const parseSegmentConfigData = () => {
  rule.value!.segments.forEach((item) => {
    item.configData = JSON.parse(item.configData)
  })
}

const sortSegmentsByOrder = () => {
  rule.value!.segments = rule.value!.segments.sort((a, b) => a.segmentOrder - b.segmentOrder)
}

const setupSegmentValues = () => {
  formData.value.segmentValues = rule.value!.segments.reduce((acc, cur) => {
    acc[cur.segmentCode] = cur.configData?.defaultValue || ''
    return acc
  }, {})
}

const setupFormRules = () => {
  rule.value!.segments.forEach((cur) => {
    if (cur.required) {
      formRules.value[cur.segmentCode] = [
        { required: true, message: '请输入' + cur.segmentName, trigger: 'blur' }
      ]
      if (cur.configData.regular) {
        formRules.value[cur.segmentCode].push({
          pattern: new RegExp(cur.configData.regular),
          message: `格式不正确，正确格式：${cur.configData.placeholder}`,
          trigger: 'blur'
        })
      }
    }
  })
}

const refreshForm = () => {
  formData.value = {
    ruleCode: rule.value?.ruleCode as any,
    segmentValues: {},
    description: '',
    addVersion: false
  }
  result.value = {}
}

const initParentData = async (parent?: any) => {
  if (!parent) {
    parentRow.value = {}
    return
  }
  parentRow.value = parent

  if (rule.value?.ruleCode == 'electronic') {
    formData.value.segmentValues = { ...parent.segmentValues }
    try {
      if (formData.value.addVersion) {
        formData.value.segmentValues['hardware'] = await NumberApi.getLastNumber(
          parent.id,
          rule.value?.ruleCode,
          'hardware'
        )
      } else {
        formData.value.segmentValues['coexistence'] = await NumberApi.getLastNumber(
          parent.id,
          rule.value?.ruleCode,
          'coexistence'
        )
      }
    } catch {
      visible.value = false
    }
  } else if (rule.value?.ruleCode == 'bom') {
    try {
      formData.value.segmentValues['distinction'] =
        (await NumberApi.getLastNumber(parent.id, rule.value?.ruleCode, 'distinction')) || '00'
    } catch {
      visible.value = false
    }

    formData.value.segmentValues['electronic'] = parent.fullNumber
  } else if (rule.value?.ruleCode == 'accessory') {
    rule.value.segments.forEach((item) => {
      if (item.segmentCode == 'electronic') {
        formData.value.segmentValues[item.segmentCode] = parent.fullNumber
      } else if (formData.value.addVersion) {
        formData.value.segmentValues[item.segmentCode] = parent.segmentValues[item.segmentCode]
      }
    })
  } else {
    formData.value.segmentValues['electronic'] = parent.fullNumber
  }
}

const openForm = async (ruleCode: string, parent?: any, newVersion: boolean = false) => {
  rule.value = await RuleConfigApi.getRule(ruleCode)
  refreshForm()
  formData.value.addVersion = newVersion
  parseSegmentConfigData()
  sortSegmentsByOrder()
  initializeFormData()
  setupSegmentValues()
  setupFormRules()
  initParentData(parent)
  visible.value = true
}

const generateCode = async () => {
  await formRef.value.validate()
  if (!formData.value.description) {
    message.error('请输入描述/备注')
    return
  }
  loading.value = true
  try {
    if (formData.value.addVersion) {
      if (formData.value.ruleCode == 'electronic' && parentRow.value?.parentId == 0) {
        const res = await NumberApi.generatePrincipleNumber({
          ...formData.value,
          sourceId: parentRow.value.id
        })
        result.value = res
      } else {
        const res = await NumberApi.generateChildNumber(parentRow.value.parentId, {
          ...formData.value,
          sourceId: parentRow.value.id
        })
        result.value = res
      }
    } else {
      if (parentRow.value?.id) {
        const res = await NumberApi.generateChildNumber(parentRow.value.id, formData.value)
        result.value = res
      } else {
        const res = await NumberApi.generatePrincipleNumber(formData.value)
        result.value = res
      }
    }
    message.success('生成成功')
    emits('success', formData.value.ruleCode, result.value)
    visible.value = false
  } finally {
    loading.value = false
  }
}

const onBeforeClose = (done: any) => {
  refreshForm()
  done()
}

defineExpose({
  openForm
})
</script>

<style lang="scss" scoped>
:deep(.el-form-item__content),
:deep(.is-disabled .el-input__wrapper),
:deep(.is-disabled.el-select__wrapper) {
  background-color: #fafafa;
  color: #000;
  box-shadow: none;
}
</style>
